import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/por/apply/item/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getPage1 = (current, size, params) => {
  return request({
    url: "/api/ni/por/apply/item/v1/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const pageWithOrder = (current, size, params) => {
  return request({
    url: "/api/ni/por/apply/item/pageWithOrder",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getList = (params) => {
  return request({
    url: "/api/ni/por/apply/item/list",
    method: "get",
    params: {
      ...params,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/por/apply/item/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/por/apply/item/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/por/apply/item/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/por/apply/item/submit",
    method: "post",
    data: row,
  });
};

export const inquiryPage = (current, size, params) => {
  return request({
    url: "/api/ni/por/apply/item/inquiryPage",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const reject = (ids, reason) => {
  return request({
    url: "/api/ni/por/apply/item/reject",
    method: "post",
    data: {
      ids,
      reason,
    },
  });
};
export const changeRemark = (ids, remark) => {
  return request({
    url: "/api/ni/por/apply/item/changeRemark",
    method: "post",
    params: {
      ids,
      remark,
    },
  });
};

export const changeInquiry = (data) => {
  return request({
    url: "/api/ni/por/apply/item/changeInquiry",
    method: "post",
    data,
  });
};

export const changePv = (ids, pv) => {
  return request({
    url: "/api/ni/por/apply/item/changePv",
    method: "post",
    params: {
      ids,
      pv,
    },
  });
}
export const changeYearsAgo = (ids, yearsAgo) => {
  return request({
    url: "/api/ni/por/apply/item/changeYearsAgo",
    method: "post",
    params: {
      ids,
      yearsAgo,
    },
  });
};
export  const changeFa=(ids,fa)=>{
  return request({
    url: "/api/ni/por/apply/item/changeFa",
    method: "post",
    params: {
      ids,
      fa,
    },
  });
}