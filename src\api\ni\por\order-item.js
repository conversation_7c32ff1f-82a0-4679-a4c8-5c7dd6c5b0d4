import request from "@/router/axios";

export const getList = (params) => {
  return request({
    url: "/api/ni/por/order/item/list",
    method: "get",
    params,
  });
};
export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/por/order/item/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getNewStockPage = (current, size, params) => {
  return request({
    url: "/api/ni/por/order/item/newStockPage",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getOldPage = (current, size, params) => {
  return request({
    url: "/api/ni/por/order/item/old/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/por/order/item/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/por/order/item/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/por/order/item/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/por/order/item/submit",
    method: "post",
    data: row,
  });
};

export const getPageWithUnOrder = (current, size, params) => {
  return request({
    url: "/api/ni/por/order/item/withUnOrderPage",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const receive = (data) => {
  return request({
    url: "/api/ni/por/order/item/receive",
    method: "post",
    data,
  });
};
export const receive1 = (ids, arrivalItemIds, depotLocation, receiveRemark) => {
  return request({
    url: "/api/ni/por/order/item/v1/receive",
    method: "post",
    params: {
      ids,
      arrivalItemIds,
      depotLocation,
      receiveRemark,
    },
  });
};
export const receiveCancel = (ids, arrivalItemIds) => {
  return request({
    url: "/api/ni/por/order/item/receiveCancel",
    method: "post",
    params: {
      ids,
      arrivalItemIds,
    },
  });
};

export const changeDepotLocation = (id, depotLocation) => {
  return request({
    url: "/api/ni/por/order/item/changeDepotLocation",
    method: "post",
    params: {
      id,
      depotLocation,
    },
  });
};
export const changeReceiveRemark = (id, receiveRemark) => {
  return request({
    url: "/api/ni/por/order/item/changeReceiveRemark",
    method: "post",
    params: {
      id,
      receiveRemark,
    },
  });
};
export const changeSupplier = (ids, supplierId) => {
  return request({
    url: "/api/ni/por/order/item/changeSupplier",
    method: "post",
    params: {
      ids,
      supplierId,
    },
  });
};
export const changePayType = (ids, payType) => {
  return request({
    url: "/api/ni/por/order/item/changePayType",
    method: "post",
    params: {
      ids,
      payType,
    },
  });
};

export const changeAlipayBusinessNo = (ids, alipayBusinessNo) => {
  return request({
    url: "/api/ni/por/order/item/changeAlipayBusinessNo",
    method: "post",
    params: {
      ids,
      alipayBusinessNo,
    },
  });
};

export const linkBill = (ids, billId) => {
  return request({
    url: "/api/ni/por/order/item/linkBill",
    method: "post",
    params: {
      ids,
      billId,
    },
  });
};

export const changeIsBill = (ids, isBill) => {
  return request({
    url: "/api/ni/por/order/item/changeIsBill",
    method: "post",
    params: {
      ids,
      isBill,
    },
  });
};
export const changeBillSerialNo = (ids, billSerialNo, billType, isBill) => {
  return request({
    url: "/api/ni/por/order/item/changeBillSerialNo",
    method: "post",
    params: {
      ids,
      billSerialNo,
      billType,
      isBill,
    },
  });
};

export const changeBillType = (ids, billType) => {
  return request({
    url: "/api/ni/por/order/item/changeBillType",
    method: "post",
    params: {
      ids,
      billType,
    },
  });
};

export const changeBillSerialNo1 = (ids, billSerialNo) => {
  return request({
    url: "/api/ni/por/order/item/changeBillSerialNo1",
    method: "post",
    params: {
      ids,
      billSerialNo,
    },
  });
};
export const changeManufacturer = (ids, manufacturer) => {
  return request({
    url: "/api/ni/por/order/item/changeManufacturer",
    method: "post",
    params: {
      ids,
      manufacturer,
    },
  });
};
export const changeTags = (ids, tags) => {
  return request({
    url: "/api/ni/por/order/item/changeTags",
    method: "post",
    params: {
      ids,
      tags,
    },
  });
};
export const reject = (ids, reason) => {
  return request({
    url: "/api/ni/por/order/item/reject",
    method: "post",
    data: {
      ids,
      reason,
    },
  });
};
export const cancel = (ids) => {
  return request({
    url: "/api/ni/por/order/item/cancel",
    method: "post",
    params: {
      ids,
    },
  });
};
export const changePv = (ids, pv) => {
  return request({
    url: "/api/ni/por/order/item/changePv",
    method: "post",
    params: {
      ids,
      pv,
    },
  });
};

export const changeFa = (ids, fa) => {
  return request({
    url: "/api/ni/por/order/item/changeFa",
    method: "post",
    params: {
      ids,
      fa,
    },
  });
};


export const mergeOrderItem = (orderId, orderItemIds) => {
  return request({
    url: "/api/ni/por/order/item/mergeOrder",
    method: "post",
    params: {
      orderId,
      orderItemIds,
    },
  });
}
export const listByApplyItemId = (applyItemId) => {
  return request({
    url: "/api/ni/por/order/item/listByApplyItemId",
    method: "post",
    params: {
      applyItemId
    },
  });
}
