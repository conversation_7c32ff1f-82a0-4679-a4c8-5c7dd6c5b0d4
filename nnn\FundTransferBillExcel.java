package com.natergy.ni.fund.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 资金转出账单导入实体类
 *
 * <AUTHOR>
 * @since 2024-03-27
 */
@Data
public class FundTransferBillExcel {

    /**
     * 收款账户名称
     */
    @ExcelProperty("收款账户名称")
    private String payeeName;

    /**
     * 金额
     */
    @ExcelProperty("金额")
    private BigDecimal amount;

    /**
     * 币种
     */
    @ExcelProperty("币种")
    private String currency;

    /**
     * 收款方开户行名称
     */
    @ExcelProperty("收款方开户行名称")
    private String payeeBank;

    /**
     * 附言
     */
    @ExcelProperty("附言")
    private String remark;

    /**
     * 支付联行号
     */
    @ExcelProperty("支付联行号")
    private String bankCode;

    /**
     * 开户局名称
     */
    @ExcelProperty("开户网点名称")
    private String bankBranch;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String note;
}
