<template>
    <basic-container>
        <el-container>
            <el-main style="margin-left: 10px" class="main_cont">
                <template>
                    <avue-crud ref="crud" :option="option" :table-loading="loading" :data="data" :page.sync="page"
                        :permission="permissionList" v-model="form" @row-update="rowUpdate" @row-save="rowSave"
                        @row-del="rowDel" @search-change="searchChange" @search-reset="searchReset"
                        @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange"
                        @on-load="onLoad" @refresh-change="onLoad(page, query)" :row-style="rowStyle"
                        @row-dblclick="handleRowDBLClick" :before-open="beforeOpen">
                        <!-- 显示文件大小 -->
                        <template #videoSize="{ row }">
                            <span v-if="!row.folder">{{ fileSizeFormat(row.videoSize) }}</span>
                            <span v-else></span>
                        </template>
                        <!-- 根据文件类型显示图标 -->
                        <template #name="{ row }">
                            <span v-if="row.folder">
                                <i class="el-icon-folder" style="color: darkorange; font-size: 24px"></i>
                                {{ row.name }}
                            </span>
                            <span v-else-if="fileTypeFormat(row.type) == 'document'">
                                <i class="el-icon-document" style="color: #a9a9a9; font-size: 18px"></i>
                                {{ row.name }}
                            </span>
                            <span v-else-if="fileTypeFormat(row.fileType) == 'img'">
                                <i class="el-icon-picture-outline" style="color: #a9a9a9; font-size: 18px"></i>
                                {{ row.name }}
                            </span>
                            <span v-else-if="fileTypeFormat(row.fileType) == 'video'">
                                <i class="el-icon-video-play" style="color: #a9a9a9; font-size: 18px"></i>
                                {{ row.name }}
                            </span>
                            <span v-else-if="fileTypeFormat(row.fileType) == 'music'">
                                <i class="el-icon-headset" style="color: #a9a9a9; font-size: 18px"></i>
                                {{ row.name }}
                            </span>
                            <span v-else-if="fileTypeFormat(row.fileType) == 'con'">
                                <i class="el-icon-collection" style="color: #a9a9a9; font-size: 18px"></i>
                                {{ row.name }}
                            </span>
                            <span v-else>{{ row.name }}</span>
                        </template>
                        <template #type="{ row }">
                            <span v-if="row.folder">文件夹</span>
                            <span v-else>{{ row.type }}</span>
                        </template>
                        <template #menuLeft>
                            <!-- 面包屑（动态添加和删除） -->
                            <div class="breadcrumbClass">
                                <el-breadcrumb separator-class="el-icon-arrow-right">
                                    <el-breadcrumb-item v-for="(item, index) in breadcrumb" :key="item.id">
                                        <span class="breadcrumbItemStyle" @click="handleBreadcrumbClick(index)">
                                            {{ item.name }}
                                        </span>
                                    </el-breadcrumb-item>
                                </el-breadcrumb>
                                <el-button class="breadcrumbBtn" @click="handleReturn" size="mini" v-if="parentId != 0">
                                    返回上一级
                                </el-button>
                            </div>
                        </template>
                        <template slot="menu" slot-scope="{ row }">
                            <el-button type="text" icon="el-icon-edit" size="mini" plain @click="handleRename(row)">
                                重命名
                            </el-button>
                            <el-button type="text" icon="el-icon-delete" size="mini" plain @click="handleDelete(row)">
                                删除
                            </el-button>
                            <el-button v-if="!row.folder" type="text" size="small" icon="el-icon-view"
                                @click="handlePreview(row)">
                                预览
                            </el-button>
                            <el-button v-if="row.isFile" type="text" size="small" icon="el-icon-download"
                                @click="handleDownload(row.videoUrl, row.name)">
                                下载
                            </el-button>
                        </template>
                    </avue-crud>
                </template>
                <!-- 重命名弹窗 -->
                <el-dialog 
                    title="重命名" 
                    :visible.sync="renameDialogVisible" 
                    width="30%" 
                    :before-close="handleClose"
                    append-to-body>
                    <el-input v-model="newName" placeholder="请输入新名称"></el-input>
                    <span slot="footer" class="dialog-footer">
                        <el-button @click="renameDialogVisible = false">取消</el-button>
                        <el-button type="primary" @click="submitRename">确定</el-button>
                    </span>
                </el-dialog>
            </el-main>
        </el-container>
    </basic-container>
</template>

<script>
import { getOfcVideoPage, ofcVideoRename, ofcVideoDelete } from "@/api/ni/ofc/ofcVideo";
import { mapGetters } from "vuex";
import { getAuthorizationHeader } from "@/api/resource/fileManagement";
import { Base64 } from "js-base64";

export default {
    data() {
        return {
            breadcrumb: [{ name: "首页", id: 0 }], // 面包屑数组，初始化为首页
            parentId: 0, // 当前文件夹ID
            form: {},
            query: {},
            loading: true,
            page: {
                pageSize: 50,
                currentPage: 1,
                total: 0,
            },
            selectionList: [],
            option: {
                rowKey: "id",
                rowParentKey: "parentId",
                size: "mini",
                height: "auto",
                calcHeight: 30,
                tip: false,
                border: false,
                selection: false,
                addBtn: false,
                editBtn: false,
                delBtn: false,
                align: "center",
                searchMenuSpan: 6,
                searchSize: "mini",
                searchIcon: true,
                searchIndex: 3,
                searchShow: false,
                searchSpan: 6,
                column: [
                    {
                        label: "文件名",
                        prop: "name",
                        search: true,
                        overHidden: true,
                        viewdisplay: true,
                        addDisplay: false,
                        editDisplay: true,
                        sortable: true,
                    },
                    {
                        label: "文件类型",
                        prop: "type",
                        display: false,
                        viewDisplay: true,
                        search: true,
                        sortable: true,
                    },
                    {
                        label: "文件大小",
                        prop: "videoSize",
                        viewdisplay: true,
                        addDisplay: false,
                        editDisplay: false,
                        sortable: true,
                    },
                    {
                        label: "创建者",
                        prop: "createUser",
                        display: false,
                        dicUrl: `/api/blade-user/user-list`,
                        search: true,
                        sortable: true,
                        type: "tree",
                        props: {
                            label: "name",
                            value: "id",
                        },
                    },
                    {
                        label: "部门",
                        prop: "createDept",
                        display: false,
                        dicUrl: `/api/blade-system/dept/list`,
                        search: true,
                        sortable: true,
                        type: "tree",
                        props: {
                            label: "deptName",
                            value: "id",
                        },
                    },
                    {
                        label: "创建日期",
                        prop: "createTimeRange",
                        type: "datetimerange",
                        format: "yyyy-MM-dd HH:mm:ss",
                        valueFormat: "yyyy-MM-dd HH:mm:ss",
                        startPlaceholder: "日期开始范围",
                        endPlaceholder: "日期结束范围",
                        search: true,
                        labelWidth: 110,
                        searchRange: true,
                        hide: true,
                        display: false,
                    },
                    {
                        label: "创建日期",
                        prop: "createTime",
                        display: false,
                        sortable: true,
                        width: 130,
                        overHidden: true,
                    },
                ],
            },
            data: [],
            header: getAuthorizationHeader(),
            renameDialogVisible: false, // 控制重命名弹窗显示
            newName: "", // 新文件名
            currentRow: null, // 当前操作的行
        };
    },
    computed: {
        ...mapGetters(["permission", "userInfo"]),
        permissionList() {
            return {
                delBtn: this.vaildData(this.permission.file_delete, false),
                editBtn: this.vaildData(this.permission.file_edit, false),
                addBtn: this.vaildData(this.permission.file_add, false),
            };
        },
        ids() {
            let ids = [];
            this.selectionList.forEach((ele) => {
                ids.push(ele.id);
            });
            return ids.join(",");
        },
    },
    activated() {
        this.$nextTick(() => {
            this.$refs.crud.doLayout();
        });
    },
    created() {
        this.onLoad(this.page);
    },
    methods: {
        // 表格行样式
        rowStyle({ row }) {
            if (row.folder) {
                return {
                    backgroundColor: "#ffffcc",
                };
            }
        },
        // 文件类型判断
        fileTypeFormat(type) {
            if (["txt", "doc", "docx", "xls", "xlsx", "pdf", "wps", "ppt", "pptx"].includes(type)) {
                return "document";
            } else if (["jpg", "jpeg", "png", "gif", "psd", "bmp"].includes(type)) {
                return "img";
            } else if (["wmv", "asf", "rm", "rmvb", "mov", "mp4", "avi"].includes(type)) {
                return "video";
            } else if (["wav", "mp3"].includes(type)) {
                return "music";
            } else if (["rar", "zip"].includes(type)) {
                return "con";
            }
        },
        // 文件大小格式化
        fileSizeFormat(size) {
            if (size == null) {
                return "未知";
            }
            if (size < 1024) {
                return size + " B";
            } else if (size < 1024 * 1024) {
                return (size / 1024).toFixed(2) + " K";
            } else if (size < 1024 * 1024 * 1024) {
                return (size / (1024 * 1024)).toFixed(2) + " M";
            } else {
                return (size / (1024 * 1024 * 1024)).toFixed(2) + " G";
            }
        },
        // 重命名
        handleRename(row) {
            this.currentRow = row;
            this.newName = row.name; // 初始化输入框为当前文件名
            this.renameDialogVisible = true; // 打开弹窗
        },
        // 提交重命名
        submitRename() {
            if (!this.newName.trim()) {
                this.$message.error("文件名不能为空");
                return;
            }
            ofcVideoRename(this.currentRow.id, this.newName).then((res) => {
                if (res.data.success) {
                    this.$message({
                        type: "success",
                        message: "重命名成功!",
                    });
                    this.onLoad(this.page); // 刷新表格数据
                    this.renameDialogVisible = false; // 关闭弹窗
                    this.newName = ""; // 清空输入框
                    this.currentRow = null; // 清空当前行
                } else {
                    this.$message.error("重命名失败: " + res.data.msg);
                }
            }).catch((err) => {
                this.$message.error("重命名失败: " + err.message);
            });
        },
        // 关闭弹窗
        handleClose(done) {
            this.$confirm("确认关闭？")
                .then(() => {
                    this.renameDialogVisible = false;
                    this.newName = "";
                    this.currentRow = null;
                    done();
                })
                .catch(() => {});
        },
        // 删除
        handleDelete(row) {
            this.$confirm("是否要删除此文件?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                ofcVideoDelete(row.id).then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!",
                    });
                });
            });
        },
        // 文件预览
        handlePreview(row) {
            const url = "http://192.168.200.23:8012/onlinePreview?url=";
            window.open(url + encodeURIComponent(Base64.encode(row.videoUrl)));
        },
        // 文件下载
        async handleDownload(videoUrl, videoName) {
            try {
                const response = await fetch(videoUrl);
                if (!response.ok) {
                    throw new Error("链接请求失败");
                }
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement("a");
                link.href = url;
                link.target = "_blank";
                link.download = videoName;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);
            } catch (error) {
                console.error(error);
            }
        },
        // 双击打开文件夹
        handleRowDBLClick(row) {
            if (row.folder) {
                this.parentId = row.id;
                this.breadcrumb.push({ name: row.name, id: row.id });
                this.page.currentPage = 1;
                this.data = [];
                this.onLoad(this.page);
            }
        },
        // 面包屑点击
        handleBreadcrumbClick(index) {
            if (index === this.breadcrumb.length - 1) return; // 点击当前层级不操作
            this.breadcrumb.splice(index + 1); // 截断点击项之后的面包屑
            this.parentId = this.breadcrumb[index].id;
            this.page.currentPage = 1;
            this.data = [];
            this.onLoad(this.page);
        },
        // 返回上一级
        handleReturn() {
            if (this.parentId != 0) {
                this.breadcrumb.pop(); // 移除最后一个面包屑
                this.parentId = this.breadcrumb[this.breadcrumb.length - 1].id;
                this.page.currentPage = 1;
                this.data = [];
                this.onLoad(this.page);
            } else {
                this.$message.warning("已经是根目录");
            }
        },
        // 搜索重置
        searchReset() {
            this.query = {};
            this.page.currentPage = 1;
            this.onLoad(this.page);
        },
        // 搜索
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1;
            this.onLoad(this.page, params);
            done();
        },
        // 选择变化
        selectionChange(list) {
            this.selectionList = list;
        },
        // 当前页变化
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
            this.onLoad(this.page, this.query);
        },
        // 每页大小变化
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
            this.onLoad(this.page, this.query);
        },
        // 加载数据
        onLoad(page, params = {}) {
            this.loading = true;
            params.parentId = this.parentId;
            getOfcVideoPage(page.currentPage, page.pageSize, Object.assign(params, this.query)).then((res) => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
            });
        },
        findObject(array, prop) {
            return array.find((item) => item.prop === prop);
        },
        vaildData(val, def) {
            return val || def;
        },
    },
};
</script>

<style lang="scss" scoped>
/deep/ .avue-crud__img {
    img {
        width: 32px;
        height: 32px;
    }
}

.breadcrumbClass {
    display: flex;
    flex-direction: row;
    margin: 15px auto 10px auto;
    align-items: center;
    justify-content: space-between;
}

.breadcrumbBtn {
    margin-left: 10px;
}

/deep/ .el-table__cell:hover {
    cursor: pointer;
}

/deep/ .el-table__cell>.cell {
    text-align: left !important;
}

.main_cont {
    position: relative;
    margin: 0;
    padding: 0;
    background-color: #e9eef3;
}

.breadcrumbItemStyle {
    float: left;
    width: auto;
    margin-right: 10px;
    text-align: center;
    position: relative;
    z-index: 2;
    font-weight: bold;
    font-size: 14px;
    cursor: pointer;
}

.breadcrumbItemStyle:hover {
    color: #409eff;
}
</style>