package com.natergy.ni.fund.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.natergy.ni.fund.service.IFundTransferBillService;
import com.natergy.ni.fund.vo.FundTransferBillImportResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 资金转出账单导入 控制器
 *
 * <AUTHOR>
 * @since 2024-03-27
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/ni/fund/transfer/bill")
@Api(value = "资金转出账单导入", tags = "资金管理")
public class FundTransferBillController {

    private final IFundTransferBillService fundTransferBillService;

    /**
     * 导入账单并核对
     */
    @PostMapping("/import")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "导入账单", notes = "导入Excel账单并与转出管理表单数据核对")
    public R<FundTransferBillImportResultVO> importBill(@RequestParam("file") MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return R.fail("请选择要导入的Excel文件");
        }

        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !originalFilename.endsWith(".xlsx")) {
            return R.fail("请上传Excel文件(.xlsx)");
        }

        try {
            FundTransferBillImportResultVO result = fundTransferBillService.importAndVerifyBill(file);
            return R.data(result);
        } catch (Exception e) {
            return R.fail("导入失败：" + e.getMessage());
        }
    }
} 