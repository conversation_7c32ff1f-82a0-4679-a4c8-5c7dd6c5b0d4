import request from '@/router/axios';



export const getSummaryByDepotSkuList = (current, size, params) => {
  return request({
    url: '/api/ni/fg/inventory/summaryByDepotSkuList',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getList = (current, size, params) => {
  return request({
    url: '/api/ni/fg/inventory/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/ni/fg/inventory/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const changeRemark = (ids, remark) => {
  return request({
    url: '/api/ni/fg/inventory/changeRemark',
    method: 'post',
    params: {
      ids,
      remark
    }
  })
}
export const getBeforeProductionDate = (depotId, skuId, productionDate) => {
  return request({
    url: '/api/ni/fg/inventory/getBeforeProductionDate',
    method: 'post',
    params: {
      depotId, skuId, productionDate
    }
  })
}

export const getBeforeBatchNo=(depotId,ids)=>{
  return request({
    url: '/api/ni/fg/inventory/getBeforeBatchNo',
    method: 'post',
    params: {
      depotId,
      ids
    }
  })
}

export const freezeCancel=(ids)=>{
  return request({
    url: '/api/ni/fg/inventory/freezeCancel',
    method: 'post',
    params: {
      ids,
    }
  })
}
