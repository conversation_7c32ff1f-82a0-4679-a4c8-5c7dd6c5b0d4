import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/base/contract/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/base/contract/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/base/contract/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/base/contract/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/base/contract/update",
    method: "post",
    data: row,
  });
};

export const end = (ids) => {
  return request({
    url: "/api/ni/base/contract/end",
    method: "post",
    params: {
      ids,
    },
  });
};

export const finish = (ids) => {
  return request({
    url: "/api/ni/base/contract/finish",
    method: "post",
    params: {
      ids,
    },
  });
};

export const toVoid = (ids) => {
  return request({
    url: "/api/ni/base/contract/toVoid",
    method: "post",
    params: {
      ids,
    },
  });
};
export const getDetailByProcessInsId = (processInsId) => {
  return request({
    url: "/api/ni/base/contract/detail",
    method: "get",
    params: {
      processInsId,
    },
  });
};

export const getPayApplyByContractId = (id) => {
  return request({
    url: "/api/ni/base/contract/getPayApplyByContractId",
    method: "get",
    params: {
      id,
    },
  });
};

export const generateArchiveNo = (id) => {
  return request({
    url: "/api/ni/base/contract/generateArchiveNo",
    method: "get",
    params: {
      id,
    },
  });
};

// 获取合同归档流程ID
export const getProcessIdByProcessKey = (processKey) => {
  return request({
    url: `/api/blade-workflow/process/getProcessIdByProcessKey`,
    method: "GET",
    params: {
      processKey,
    },
  });
};

//发起流程
export const archiveStart = (row, processDefKey) => {
  return request({
    url: "/api/ni/base/contract/archiveStart",
    method: "post",
    params: {
      processDefKey,
    },
    data: row,
  });
};

//获取taskId
export const getTaskIdByProcessInsId = (processInsId) => {
  return request({
    url: `/api/ni/base/contract/getTaskIdByProcessInsId`,
    method: "GET",
    params: {
      processInsId,
    },
  });
};

export const buildContractByOrder = (orderId) => {
  return request({
    url: `/api/ni/base/contract/buildContractByOrder`,
    method: "POST",
    params: {
      orderId,
    },
  });
};
export const apply = (id, processDefKey) => {
  return request({
    url: "/api/ni/base/contract/apply",
    method: "post",
    params: {
      id,
      processDefKey,
    },
  });
};
