package com.natergy.ni.fund.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 资金转出账单导入结果VO
 *
 * <AUTHOR>
 * @since 2024-03-27
 */
@Data
public class FundTransferBillImportResultVO {

    /**
     * 导入总条数
     */
    private Integer totalCount;

    /**
     * 匹配成功条数
     */
    private Integer matchedCount;

    /**
     * 不匹配条数
     */
    private Integer unmatchedCount;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 匹配成功金额
     */
    private BigDecimal matchedAmount;

    /**
     * 不匹配金额
     */
    private BigDecimal unmatchedAmount;

    /**
     * 匹配成功的记录
     */
    private List<MatchedRecord> matchedRecords;

    /**
     * 不匹配的记录
     */
    private List<UnmatchedRecord> unmatchedRecords;

    @Data
    public static class MatchedRecord {
        /**
         * Excel中的记录
         */
        private String excelRecord;

        /**
         * 匹配的转出申请信息
         */
        private String transactionInfo;
    }

    @Data
    public static class UnmatchedRecord {
        /**
         * Excel中的记录
         */
        private String excelRecord;

        /**
         * 不匹配原因
         */
        private String reason;
    }
} 