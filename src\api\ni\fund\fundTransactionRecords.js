import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/fundTransactionRecords/niFundTransactionRecords/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}


export const getPage = (current, size, params) => {
  return request({
    url: '/api/fundTransactionRecords/niFundTransactionRecords/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/fundTransactionRecords/niFundTransactionRecords/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/fundTransactionRecords/niFundTransactionRecords/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/fundTransactionRecords/niFundTransactionRecords/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/fundTransactionRecords/niFundTransactionRecords/submit',
    method: 'post',
    data: row
  })
}



export const getTransactionInfo = (id) => {
  return request({
    url: '/api/fundTransactionRecords/niFundTransactionRecords/getTransactionInfo',
    method: 'get',
    params: {
      id
    }
  })
}




export const confirmPayment = (id) => {
  return request({
    url: '/api/fundTransactionRecords/niFundTransactionRecords/confirmPayment',
    method: 'post',
    params: {
      id
    }
  })
}

