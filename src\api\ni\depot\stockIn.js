import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/ni/depot/stock/in/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/depot/stock/in/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/depot/stock/in/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/depot/stock/in/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/depot/stock/in/update",
    method: "post",
    data: row,
  });
};

export const submit = (ids) => {
  return request({
    url: "/api/ni/depot/stock/in/submit",
    method: "post",
    params: {
      ids,
    },
  });
};

export const toVoid = (ids) => {
  return request({
    url: "/api/ni/depot/stock/in/toVoid",
    method: "post",
    params: {
      ids,
    },
  });
};

export const getList1 = (params) => {
  return request({
    url: "/api/ni/depot/stock/in/list",
    method: "get",
    params: {
      ...params,
    },
  });
};

export const sync = (id, isCovered = false) => {
  return request({
    url: "/api/ni/depot/stock/in/sync",
    method: "post",
    params: {
      id,
      isCovered,
    },
  });
};

export const inReturn = (data) => {
  return request({
    url: "/api/ni/depot/stock/in/inReturn",
    method: "post",
    data,
  });
};
export const syncRange = (startDate, endDate) => {
  return request({
    url: "/api/ni/depot/stock/in/syncRange",
    method: "post",
    params: {
      startDate,
      endDate,
    },
  });
};
export const stockInByArrivalItems = (depotId, pv, arrivalItemIds) => {
  return request({
    url: "/api/ni/depot/stock/in/stockInByArrivalItems",
    method: "post",
    params: {
      depotId,
      pv,
      arrivalItemIds,
    },
  });
};
