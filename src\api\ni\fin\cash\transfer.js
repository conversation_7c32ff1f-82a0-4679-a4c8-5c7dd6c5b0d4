import request from "@/router/axios";

export const getPage = (current, size, params) => {
  return request({
    url: "/api/ni/fin/cash/transfer/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/ni/fin/cash/transfer/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/ni/fin/cash/transfer/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/ni/fin/cash/transfer/save",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/ni/fin/cash/transfer/update",
    method: "post",
    data: row,
  });
};

export const submit = (ids) => {
  return request({
    url: "/api/ni/fin/cash/transfer/submit",
    method: "post",
    params: {
      ids,
    },
  });
};

export const back = (ids) => {
  return request({
    url: "/api/ni/fin/cash/transfer/back",
    method: "post",
    params: {
      ids,
    },
  });
};

export const toVoid = (ids) => {
  return request({
    url: "/api/ni/fin/cash/transfer/toVoid",
    method: "post",
    params: {
      ids,
    },
  });
};
