<template>
  <basic-container>
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane label="待审核" name="first"></el-tab-pane>
      <el-tab-pane label="已完结" name="second"></el-tab-pane>
    </el-tabs>
    <div>
      <avue-crud
        v-if="activeName === 'first'"
        :option="{ ...option, selection: true }"
        :table-loading="loading"
        :data="data"
        :page.sync="page"
        :permission="permissionList"
        :before-open="beforeOpen"
        v-model="form"
        ref="crud"
        @search-change="searchChange"
        @search-reset="searchReset"
        @current-change="currentChange"
        @size-change="sizeChange"
        @refresh-change="refreshChange"
        @on-load="onLoad"
        @selection-change="selectionChange"
      >
        <template #transactionAmount="{ row }">
          {{ row.transactionAmount ? row.transactionAmount : "所有余额" }}
        </template>

        <template #paymentStatus="{ row }">
          <el-tag v-if="row.paymentStatus === 1" type="success">已打款</el-tag>
          <el-tag v-else type="warning">未打款</el-tag>
        </template>

        <template slot="menu" slot-scope="{ row, size }">
          <el-button
            type="text"
            icon="el-icon-info"
            :size="size"
            v-if="permission.fundTransactionRecords_viewFlow"
            @click="handleFlowInfo(row)"
            >流程详情
          </el-button>

          <el-button
            type="text"
            icon="el-icon-printer"
            :size="size"
            v-if="permission.fundTransactionRecords_print"
            @click="printPayOrder(row)"
            >打印付款单
          </el-button>

          <el-button
            type="text"
            :size="size"
            icon="el-icon-success"
             @click="handleSingleConfirmPayment(row)"
            >确认打款
          </el-button>
        </template>

        <!-- 待审核页面的导出按钮 -->
        <template #menuLeft>
          <el-button
            size="mini"
            icon="el-icon-download"
            type="success"
            @click="handleExport"
            >导出
          </el-button>

          <el-button
            size="mini"
            icon="el-icon-success"
            type="warning"
            @click="handleConfirmPayment"
            >确认打款
          </el-button>
          <el-button
            size="mini"
            icon="el-icon-delete"
            type="danger"
            @click="handleDelete"
            >删除
          </el-button>
        </template>
      </avue-crud>
    </div>

    <div>
      <avue-crud
        v-if="activeName === 'second'"
        :option="option"
        :table-loading="loadingCompleted"
        :data="dataCompleted"
        :page.sync="pageCompleted"
        :permission="permissionList"
        :before-open="beforeOpen"
        v-model="formCompleted"
        ref="crudSecond"
        @search-change="searchChangeCompleted"
        @search-reset="searchResetCompleted"
        @current-change="currentChangeCompleted"
        @size-change="sizeChangeCompleted"
        @refresh-change="refreshChangeCompleted"
        @on-load="onLoadCompleted"
      >
        <template #paymentStatus="{ row }">
          <el-tag v-if="row.paymentStatus === 1" type="success">已打款</el-tag>
          <el-tag v-else type="warning">未打款</el-tag>
        </template>
        <template slot="menu" slot-scope="{ row, size }">
          <el-button
            type="text"
            icon="el-icon-info"
            :size="size"
            v-if="permission.fundTransactionRecords_viewFlow"
            @click="handleFlowInfo(row)"
            >流程详情
          </el-button>
          
          <el-button
            v-if="
              row.paymentStatus != 1 && permission.fundTransactionRecords_print
            "
            type="text"
            icon="el-icon-money"
            :size="size"
            @click="confirmPayment(row)"
            >确认已打款
          </el-button>
        </template>

        <!-- 已完结页面的导出按钮 -->
        <template #menuLeft>
          <el-button
            size="mini"
            icon="el-icon-download"
            type="success"
            @click="handleExportCompleted"
            >导出
          </el-button>
        </template>
      </avue-crud>
    </div>
  </basic-container>
</template>

<script>
import {
  getPage,
  getDetail,
  getTransactionInfo,
  confirmPayment,
  update
} from "@/api/ni/fund/fundTransactionRecords";
import { stopCalculatingInterest } from "@/api/ni/fund/fundIntoAccount";
import { defaultFundTransactionRecords } from "@/const/ni/fund/fundTransactionRecords";
import { mapGetters } from "vuex";
import { hiprint } from "vue-plugin-hiprint";
import { loadPrintTemplate } from "@/api/system/printTemplate";
import { el } from "@fullcalendar/core/internal-common";
export default {
  data() {
    return {
      activeName: "first",
      option: defaultFundTransactionRecords,
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      data: [],

      formCompleted: {},
      queryCompleted: {},
      loadingCompleted: true,
      pageCompleted: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      dataCompleted: [],

      printTemplateJson: {},
      hiprintTemplate: null,
      exportData: [],
      selectedRows: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: false,
        viewBtn: false,
        delBtn: false,
        editBtn: false,
      };
    },
  },
  methods: {
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },

    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      params.status = 0;
      getPage(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
      });
    },

    searchResetCompleted() {
      this.queryCompleted = {};
      this.onLoadCompleted(this.pageCompleted);
    },
    searchChangeCompleted(params, done) {
      this.queryCompleted = params;
      this.pageCompleted.currentPage = 1;
      this.onLoadCompleted(this.pageCompleted, params);
      done();
    },

    currentChangeCompleted(currentPage) {
      this.pageCompleted.currentPage = currentPage;
    },
    sizeChangeCompleted(pageSize) {
      this.pageCompleted.pageSize = pageSize;
    },
    refreshChangeCompleted() {
      this.onLoadCompleted(this.pageCompleted, this.queryCompleted);
    },
    onLoadCompleted(page, params = {}) {
      this.loadingCompleted = true;
      params.status = 1;
      getPage(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.queryCompleted)
      ).then((res) => {
        const data = res.data.data;
        this.pageCompleted.total = data.total;
        this.dataCompleted = data.records;
        this.loadingCompleted = false;
      });
    },
    handleFlowInfo({ processInstanceId }) {
      const urlParam = Buffer.from(
        JSON.stringify({ processInsId: processInstanceId })
      ).toString("base64");
      this.$router.push(`/workflow/process/detail/${urlParam}`);
    },
    printPayOrder(row) {
      if (row.withdrawalType === 1) {
        this.$confirm(
          "当前转出申请为全部转出，打印付款单后，将停止此存单的计息?",
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        ).then(() => {
          //先停止计息
          stopCalculatingInterest(row.intoAccountId).then((res) => {
            this.handlePrint(row.id);
          });
        });
      } else {
        this.handlePrint(row.id);
      }
    },

    /**
     * 确认已打款
     */
    confirmPayment(row) {
      this.$confirm(
        "请确认已完成了打款?",
        "确认打款提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        //确认完成打款
        confirmPayment(row.id).then(() => {
          this.onLoadCompleted(this.pageCompleted);
        });
      });
    },

    async handlePrint(id) {
      const loading = this.$loading({
        lock: false,
        text: "正在准备打印中...",
        spinner: "el-icon-loading",
      });

      if (!this.hiprintTemplate) {
        try {
          await this.getPrintTempJsonData();
        } catch (error) {
          console.log(error);
        }
      }
      getTransactionInfo(id)
        .then((res) => {
          const data = res.data.data;
          //   data.forEach(item=>item.statusText=item.status?'生效':'作废')
          this.hiprintTemplate.print(data);
        })
        .finally(() => {
          loading.close();
        });
    },
    async getPrintTempJsonData() {
      const res = await loadPrintTemplate("ni_fund_pay_order");

      this.printTemplateJson = JSON.parse(res.data.data.content);

      this.hiprintTemplate = new hiprint.PrintTemplate({
        template: this.printTemplateJson,
      });
    },

    async getExportData(status) {
      this.exportData = [];
      const promises = [];
      const currentPage = status === 0 ? this.page : this.pageCompleted;
      const currentQuery = status === 0 ? this.query : this.queryCompleted;
      
      for (var i = 1; i <= currentPage.total / currentPage.pageSize + 1; i++) {
        const promise = getPage(i, currentPage.pageSize, {
          ...currentQuery,
          status: status
        }).then((res) => {
          const data = res.data.data.records;
          this.exportData = this.exportData.concat(data);
        });
        promises.push(promise);
      }

      await Promise.all(promises);
      
      // 按交易时间排序
      this.exportData.sort((a, b) => {
        return new Date(b.transactionDate) - new Date(a.transactionDate);
      });
      return this.exportData;
    },

    async handleExportData() {
      let opt = {
        column: [
          { label: '申请人', prop: 'userName' },
          { label: '合同编号', prop: 'contractNumber' },
          { label: '取款类型', prop: 'withdrawalType' },
          { label: '交易金额', prop: 'transactionAmount' },
          { label: '交易类型', prop: 'transactionType' },
          { label: '交易状态', prop: 'status' },
          { label: '是否打款', prop: 'paymentStatus' },
          { label: '交易时间', prop: 'transactionDate', width: 130 },
          { label: '操作日期', prop: 'createTime', width: 130 }
        ]
      }

      await this.getExportData(0);
      this.$Export.excel({
        title: "利息转出待审核记录",
        columns: opt.column,
        data: this.exportData.map((item) => {
          return {
            ...item,
            withdrawalType: item.withdrawalType === 1 ? '全部取出' : '部分取出',
            transactionType: item.transactionType === 0 ? '转出' : '转入',
            status: ['待处理', '交易完成', '交易取消'][item.status],
            paymentStatus: item.paymentStatus === 1 ? '已打款' : '未打款'
          }
        }),
      });
      this.exportData = [];
    },

    async handleExportDataCompleted() {
      let opt = {
        column: [
          { label: '申请人', prop: 'userName' },
          { label: '合同编号', prop: 'contractNumber' },
          { label: '取款类型', prop: 'withdrawalType' },
          { label: '交易金额', prop: 'transactionAmount' },
          { label: '交易类型', prop: 'transactionType' },
          { label: '交易状态', prop: 'status' },
          { label: '是否打款', prop: 'paymentStatus' },
          { label: '交易时间', prop: 'transactionDate', width: 130 },
          { label: '操作日期', prop: 'createTime', width: 130 }
        ]
      }

      await this.getExportData(1);
      this.$Export.excel({
        title: "转出申请已完结记录",
        columns: opt.column,
        data: this.exportData.map((item) => {
          return {
            ...item,
            withdrawalType: item.withdrawalType === 1 ? '全部取出' : '部分取出',
            transactionType: item.transactionType === 0 ? '转出' : '转入',
            status: ['待处理', '交易完成', '交易取消'][item.status],
            paymentStatus: item.paymentStatus === 1 ? '已打款' : '未打款'
          }
        }),
      });
      this.exportData = [];
    },

    handleExport() {
      this.$confirm("是否导出当前筛选的所有待审核数据？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.handleExportData();
      });
    },

    handleExportCompleted() {
      this.$confirm("是否导出当前筛选的所有已完结数据？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.handleExportDataCompleted();
      });
    },

    selectionChange(selection) {
      this.selectedRows = selection;
    },

     handleSingleConfirmPayment(row) {
      this.$confirm("请确认已完成了打款?", "确认打款提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        row.paymentStatus = 1;
        row.status = 1;
        update(row).then(() => {
          this.$alert("确认已打款成功！", "提示", {
            type: "success",
            confirmButtonText: "确定"
          });
          this.onLoad(this.page);
        });
      });
    },

    handleConfirmPayment() {
      if (this.selectedRows.length === 0) {
        this.$message.warning("请选择至少一条数据！");
        return;
      }

      this.$confirm("请确认已完成了打款?", "确认打款提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const promises = this.selectedRows.map((row) => {
          row.paymentStatus = 1;
          row.status = 1;
          return update(row);
        });

        Promise.all(promises).then(() => {
          this.$alert("确认已打款成功！", "提示", {
            type: "success",
            confirmButtonText: "确定"
          });
          this.onLoad(this.page);
        });
      });
    },
  },
};
</script>

<style></style>
