package com.natergy.ni.fund.service;

import com.natergy.ni.fund.excel.FundTransferBillExcel;
import com.natergy.ni.fund.vo.FundTransferBillImportResultVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 资金转出账单导入 服务类
 *
 * <AUTHOR>
 * @since 2024-03-27
 */
public interface IFundTransferBillService {

    /**
     * 导入账单并核对
     *
     * @param file Excel文件
     * @return 导入结果
     */
    FundTransferBillImportResultVO importAndVerifyBill(MultipartFile file);

    /**
     * 解析Excel文件
     *
     * @param file Excel文件
     * @return Excel数据列表
     */
    List<FundTransferBillExcel> parseExcel(MultipartFile file);

    /**
     * 核对账单数据
     *
     * @param excelDataList Excel数据列表
     * @return 核对结果
     */
    FundTransferBillImportResultVO verifyBillData(List<FundTransferBillExcel> excelDataList);
} 