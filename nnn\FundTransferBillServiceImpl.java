package com.natergy.ni.fund.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.natergy.ni.fund.excel.FundTransferBillExcel;
import com.natergy.ni.fund.entity.FundTransactionRecordsEntity;
import com.natergy.ni.fund.service.IFundTransactionRecordsService;
import com.natergy.ni.fund.service.IFundTransferBillService;
import com.natergy.ni.fund.vo.FundTransferBillImportResultVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 资金转出账单导入 服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FundTransferBillServiceImpl implements IFundTransferBillService {

    private final IFundTransactionRecordsService transactionRecordsService;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public FundTransferBillImportResultVO importAndVerifyBill(MultipartFile file) {
        // 1. 解析Excel文件
        List<FundTransferBillExcel> excelDataList = parseExcel(file);
        
        // 2. 核对数据
        return verifyBillData(excelDataList);
    }

    @Override
    public List<FundTransferBillExcel> parseExcel(MultipartFile file) {
        try {
            return EasyExcel.read(file.getInputStream())
                    .head(FundTransferBillExcel.class)
                    .sheet()
                    .doReadSync();
        } catch (IOException e) {
            log.error("解析Excel文件失败", e);
            throw new RuntimeException("解析Excel文件失败");
        }
    }

    @Override
    public FundTransferBillImportResultVO verifyBillData(List<FundTransferBillExcel> excelDataList) {
        FundTransferBillImportResultVO result = new FundTransferBillImportResultVO();
        result.setTotalCount(excelDataList.size());
        result.setTotalAmount(BigDecimal.ZERO);
        result.setMatchedAmount(BigDecimal.ZERO);
        result.setUnmatchedAmount(BigDecimal.ZERO);
        result.setMatchedCount(0);
        result.setUnmatchedCount(0);
        result.setMatchedRecords(new ArrayList<>());
        result.setUnmatchedRecords(new ArrayList<>());

        for (FundTransferBillExcel excelData : excelDataList) {
            // 累加总金额
            result.setTotalAmount(result.getTotalAmount().add(excelData.getAmount()));

            // 验证必填字段
            if (StringUtils.isBlank(excelData.getPayeeName()) || 
                excelData.getAmount() == null || 
                StringUtils.isBlank(excelData.getPayeeBank()) ||
                StringUtils.isBlank(excelData.getBankCode())) {
                
                addUnmatchedRecord(result, excelData, "必填字段缺失");
                continue;
            }

            // 验证币种是否为人民币
            if (!"人民币".equals(excelData.getCurrency())) {
                addUnmatchedRecord(result, excelData, "币种必须为人民币");
                continue;
            }

            // 验证金额是否大于0
            if (excelData.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                addUnmatchedRecord(result, excelData, "金额必须大于0");
                continue;
            }

            // 查找匹配的转出申请记录
            List<FundTransactionRecordsEntity> matchingRecords = findMatchingTransactionRecords(excelData);

            if (matchingRecords.isEmpty()) {
                addUnmatchedRecord(result, excelData, "未找到匹配的转出申请记录");
                continue;
            }

            // 如果找到多条匹配记录，选择第一条（可以根据业务需求调整选择逻辑）
            FundTransactionRecordsEntity matchedRecord = matchingRecords.get(0);
            addMatchedRecord(result, excelData, matchedRecord);

            // 更新统计信息
            result.setMatchedCount(result.getMatchedCount() + 1);
            result.setMatchedAmount(result.getMatchedAmount().add(excelData.getAmount()));
        }

        // 计算不匹配数量和金额
        result.setUnmatchedCount(result.getTotalCount() - result.getMatchedCount());
        result.setUnmatchedAmount(result.getTotalAmount().subtract(result.getMatchedAmount()));

        return result;
    }

    private List<FundTransactionRecordsEntity> findMatchingTransactionRecords(FundTransferBillExcel excelData) {
        // 构建查询条件
        LambdaQueryWrapper<FundTransactionRecordsEntity> queryWrapper = new LambdaQueryWrapper<>();
        
        // 匹配交易金额和类型
        queryWrapper.eq(FundTransactionRecordsEntity::getTransactionAmount, excelData.getAmount())
                   .eq(FundTransactionRecordsEntity::getTransactionType, 0) // 转出类型
                   .in(FundTransactionRecordsEntity::getStatus, Arrays.asList(0, 1)) // 待处理或已完成
                   .eq(FundTransactionRecordsEntity::getPaymentStatus, 0) // 未打款
                   .orderByDesc(FundTransactionRecordsEntity::getCreateTime);

        return transactionRecordsService.list(queryWrapper);
    }

    private void addUnmatchedRecord(FundTransferBillImportResultVO result, FundTransferBillExcel excelData, String reason) {
        FundTransferBillImportResultVO.UnmatchedRecord unmatchedRecord = new FundTransferBillImportResultVO.UnmatchedRecord();
        unmatchedRecord.setExcelRecord(String.format("收款方：%s，金额：%s，开户行：%s，联行号：%s", 
            excelData.getPayeeName(), 
            excelData.getAmount(), 
            excelData.getPayeeBank(),
            excelData.getBankCode()));
        unmatchedRecord.setReason(reason);
        result.getUnmatchedRecords().add(unmatchedRecord);
    }

    private void addMatchedRecord(FundTransferBillImportResultVO result, FundTransferBillExcel excelData, FundTransactionRecordsEntity matchedRecord) {
        FundTransferBillImportResultVO.MatchedRecord matchedRecordVO = new FundTransferBillImportResultVO.MatchedRecord();
        matchedRecordVO.setExcelRecord(String.format("收款方：%s，金额：%s，开户行：%s，联行号：%s", 
            excelData.getPayeeName(), 
            excelData.getAmount(), 
            excelData.getPayeeBank(),
            excelData.getBankCode()));
        matchedRecordVO.setTransactionInfo(String.format("转出申请ID：%d，交易时间：%s，交易金额：%s", 
            matchedRecord.getId(), 
            matchedRecord.getTransactionDate() != null ? matchedRecord.getTransactionDate().format(DATE_FORMATTER) : "未设置",
            matchedRecord.getTransactionAmount()));
        result.getMatchedRecords().add(matchedRecordVO);
    }
} 